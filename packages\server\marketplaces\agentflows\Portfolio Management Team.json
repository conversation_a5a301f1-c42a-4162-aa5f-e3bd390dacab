{"description": "A team of portfolio manager, financial analyst, and risk manager working together to optimize an investment portfolio.", "framework": ["Langchain"], "usecases": ["Finance & Accounting", "Hierarchical Agent Teams"], "nodes": [{"id": "supervisor_0", "position": {"x": 242.*************, "y": 185.**************}, "type": "customNode", "data": {"id": "supervisor_0", "label": "Supervisor", "version": 1, "name": "supervisor", "type": "Supervisor", "baseClasses": ["Supervisor"], "category": "Multi Agents", "inputParams": [{"label": "Supervisor Name", "name": "<PERSON><PERSON><PERSON>", "type": "string", "placeholder": "Supervisor", "default": "Supervisor", "id": "supervisor_0-input-supervisorName-string"}, {"label": "Supervisor Prompt", "name": "supervisor<PERSON><PERSON><PERSON>", "type": "string", "description": "Prompt must contains {team_members}", "rows": 4, "default": "You are a supervisor tasked with managing a conversation between the following workers: {team_members}.\nGiven the following user request, respond with the worker to act next.\nEach worker will perform a task and respond with their results and status.\nWhen finished, respond with FINISH.\nSelect strategically to minimize the number of steps taken.", "additionalParams": true, "id": "supervisor_0-input-supervisorPrompt-string"}, {"label": "Recursion Limit", "name": "recursionLimit", "type": "number", "description": "Maximum number of times a call can recurse. If not provided, defaults to 100.", "default": 100, "additionalParams": true, "id": "supervisor_0-input-recursionLimit-number"}], "inputAnchors": [{"label": "Tool Calling Chat Model", "name": "model", "type": "BaseChatModel", "description": "Only compatible with models that are capable of function calling: ChatOpenAI, ChatMistral, ChatAnthropic, ChatGoogleGenerativeAI, GroqChat. Best result with GPT-4 model", "id": "supervisor_0-input-model-BaseChatModel"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "supervisor_0-input-inputModeration-Moderation"}], "inputs": {"supervisorName": "Supervisor", "supervisorPrompt": "You are a supervisor tasked with managing a conversation between the following workers: {team_members}.\nGiven the following user request, respond with the worker to act next.\nEach worker will perform a task and respond with their results and status.\nWhen finished, respond with FINISH.\nSelect strategically to minimize the number of steps taken.", "model": "{{chatOpenAI_0.data.instance}}", "recursionLimit": 100, "inputModeration": ""}, "outputAnchors": [{"id": "supervisor_0-output-supervisor-Supervisor", "name": "supervisor", "label": "Supervisor", "description": "", "type": "Supervisor"}], "outputs": {}, "selected": false}, "width": 300, "height": 431, "selected": false, "positionAbsolute": {"x": 242.*************, "y": 185.**************}, "dragging": false}, {"id": "worker_0", "position": {"x": 637.3247841463353, "y": 115.189653148269}, "type": "customNode", "data": {"id": "worker_0", "label": "Worker", "version": 1, "name": "worker", "type": "Worker", "baseClasses": ["Worker"], "category": "Multi Agents", "inputParams": [{"label": "Worker Name", "name": "worker<PERSON>ame", "type": "string", "placeholder": "Worker", "id": "worker_0-input-workerName-string"}, {"label": "Worker Prompt", "name": "workerPrompt", "type": "string", "rows": 4, "default": "You are a research assistant who can search for up-to-date info using search engine.", "id": "worker_0-input-workerPrompt-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "worker_0-input-promptValues-json"}, {"label": "Max Iterations", "name": "maxIterations", "type": "number", "optional": true, "id": "worker_0-input-maxIterations-number"}], "inputAnchors": [{"label": "Tools", "name": "tools", "type": "Tool", "list": true, "optional": true, "id": "worker_0-input-tools-Tool"}, {"label": "Supervisor", "name": "supervisor", "type": "Supervisor", "id": "worker_0-input-supervisor-Supervisor"}, {"label": "Tool Calling Chat Model", "name": "model", "type": "BaseChatModel", "optional": true, "description": "Only compatible with models that are capable of function calling: ChatOpenAI, ChatMistral, ChatAnthropic, ChatGoogleGenerativeAI, ChatVertexAI, GroqChat. If not specified, supervisor's model will be used", "id": "worker_0-input-model-BaseChatModel"}], "inputs": {"workerName": "Portfolio Manager", "workerPrompt": "As the Portfolio Manager at {company}, you play a crucial role in overseeing and optimizing our investment portfolio. Your expertise in market analysis, strategic planning, and risk management is essential for making informed investment decisions that drive our financial growth.\n\nYour goal is to develop and implement effective investment strategies that align with our clients' financial goals and risk tolerance.\n\nAnalyze market trends, economic data, and financial reports to identify potential investment opportunities. Collaborate with the Financial Analyst and Risk Manager to ensure that your strategies are well-informed and balanced. Continuously monitor the portfolio's performance and make adjustments as necessary to maximize returns while managing risk.\n\nYour task is to create a comprehensive investment strategy for {portfolio_name}, taking into account the client's financial objectives and risk tolerance. Ensure that your strategy is backed by thorough market research and financial analysis, and includes a plan for regular performance reviews and adjustments.\n\nThe output should be a detailed investment strategy report for {portfolio_name}, including market analysis, recommended investments, risk management approaches, and performance monitoring plans. Ensure that the strategy is designed to achieve the client's financial goals while maintaining an appropriate risk level.", "tools": ["{{googleCustomSearch_0.data.instance}}"], "supervisor": "{{supervisor_0.data.instance}}", "model": "", "promptValues": "{\"company\":\"Flowise Inc\",\"portfolio_name\":\"Tesla Inc\"}", "maxIterations": ""}, "outputAnchors": [{"id": "worker_0-output-worker-Worker", "name": "worker", "label": "Worker", "description": "", "type": "Worker"}], "outputs": {}, "selected": false}, "width": 300, "height": 808, "selected": false, "positionAbsolute": {"x": 637.3247841463353, "y": 115.189653148269}, "dragging": false}, {"id": "worker_1", "position": {"x": 1037.3247841463353, "y": 115.189653148269}, "type": "customNode", "data": {"id": "worker_1", "label": "Worker", "version": 1, "name": "worker", "type": "Worker", "baseClasses": ["Worker"], "category": "Multi Agents", "inputParams": [{"label": "Worker Name", "name": "worker<PERSON>ame", "type": "string", "placeholder": "Worker", "id": "worker_1-input-workerName-string"}, {"label": "Worker Prompt", "name": "workerPrompt", "type": "string", "rows": 4, "default": "You are a research assistant who can search for up-to-date info using search engine.", "id": "worker_1-input-workerPrompt-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "worker_1-input-promptValues-json"}, {"label": "Max Iterations", "name": "maxIterations", "type": "number", "optional": true, "id": "worker_1-input-maxIterations-number"}], "inputAnchors": [{"label": "Tools", "name": "tools", "type": "Tool", "list": true, "optional": true, "id": "worker_1-input-tools-Tool"}, {"label": "Supervisor", "name": "supervisor", "type": "Supervisor", "id": "worker_1-input-supervisor-Supervisor"}, {"label": "Tool Calling Chat Model", "name": "model", "type": "BaseChatModel", "optional": true, "description": "Only compatible with models that are capable of function calling: ChatOpenAI, ChatMistral, ChatAnthropic, ChatGoogleGenerativeAI, ChatVertexAI, GroqChat. If not specified, supervisor's model will be used", "id": "worker_1-input-model-BaseChatModel"}], "inputs": {"workerName": "Financial Analyst", "workerPrompt": "As a Financial Analyst at {company}, you are a vital member of our portfolio management team, providing in-depth research and analysis to support informed investment decisions. Your analytical skills and market insights are key to identifying profitable opportunities and enhancing the overall performance of our portfolio.\n\nYour goal is to conduct thorough financial analysis and market research to support the Portfolio Manager in developing effective investment strategies.\n\nAnalyze financial data, market trends, and economic indicators to identify potential investment opportunities. Prepare detailed reports and presentations that highlight your findings and recommendations. Collaborate closely with the Portfolio Manager and Risk Manager to ensure that your analyses contribute to well-informed and balanced investment strategies.\n\nYour task is to perform a comprehensive analysis of {investment_opportunity} for inclusion in {portfolio_name}. Use various financial metrics and market data to evaluate the potential risks and returns. Provide clear, actionable insights and recommendations based on your analysis.\n\nThe output should be a detailed financial analysis report for {investment_opportunity}, including key financial metrics, market trends, risk assessment, and your investment recommendation. Ensure that the report is well-supported by data and provides valuable insights to inform the Portfolio Manager's decision-making process.", "tools": ["{{googleCustomSearch_1.data.instance}}"], "supervisor": "{{supervisor_0.data.instance}}", "model": "", "promptValues": "{\"company\":\"Flowise Inc\",\"investment_opportunity\":\"Tech Summit Fund\",\"portfolio_name\":\"Tesla Inc\"}", "maxIterations": ""}, "outputAnchors": [{"id": "worker_1-output-worker-Worker", "name": "worker", "label": "Worker", "description": "", "type": "Worker"}], "outputs": {}, "selected": false}, "width": 300, "height": 808, "selected": false, "positionAbsolute": {"x": 1037.3247841463353, "y": 115.189653148269}, "dragging": false}, {"id": "worker_2", "position": {"x": 1482.836195011232, "y": 119.54481208270889}, "type": "customNode", "data": {"id": "worker_2", "label": "Worker", "version": 1, "name": "worker", "type": "Worker", "baseClasses": ["Worker"], "category": "Multi Agents", "inputParams": [{"label": "Worker Name", "name": "worker<PERSON>ame", "type": "string", "placeholder": "Worker", "id": "worker_2-input-workerName-string"}, {"label": "Worker Prompt", "name": "workerPrompt", "type": "string", "rows": 4, "default": "You are a research assistant who can search for up-to-date info using search engine.", "id": "worker_2-input-workerPrompt-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "worker_2-input-promptVal<PERSON>-json"}, {"label": "Max Iterations", "name": "maxIterations", "type": "number", "optional": true, "id": "worker_2-input-maxIterations-number"}], "inputAnchors": [{"label": "Tools", "name": "tools", "type": "Tool", "list": true, "optional": true, "id": "worker_2-input-tools-Tool"}, {"label": "Supervisor", "name": "supervisor", "type": "Supervisor", "id": "worker_2-input-supervisor-Supervisor"}, {"label": "Tool Calling Chat Model", "name": "model", "type": "BaseChatModel", "optional": true, "description": "Only compatible with models that are capable of function calling: ChatOpenAI, ChatMistral, ChatAnthropic, ChatGoogleGenerativeAI, ChatVertexAI, GroqChat. If not specified, supervisor's model will be used", "id": "worker_2-input-model-BaseChatModel"}], "inputs": {"workerName": "Risk Manager", "workerPrompt": "As the Risk Manager at {company}, you play a pivotal role in ensuring the stability and resilience of our investment portfolio. Your expertise in risk assessment and mitigation is essential for maintaining an appropriate balance between risk and return, aligning with our clients' risk tolerance and financial goals.\n\nYour goal is to identify, assess, and mitigate risks associated with the investment portfolio to safeguard its performance and align with our clients' risk tolerance.\n\nEvaluate potential risks for current and prospective investments using quantitative and qualitative analysis. Collaborate with the Portfolio Manager and Financial Analyst to integrate risk management strategies into the overall investment approach. Continuously monitor the portfolio to identify emerging risks and implement measures to mitigate them.\n\nYour task is to perform a comprehensive risk assessment for {portfolio_name}, focusing on potential market, credit, and operational risks. Develop and recommend risk mitigation strategies that align with the client's risk tolerance and investment objectives.\n\nThe output should be a detailed risk assessment report for {portfolio_name}, including identification of key risks, risk metrics, and recommended mitigation strategies. Ensure that the report provides actionable insights and supports the Portfolio Manager in maintaining a balanced and resilient portfolio.", "tools": ["{{googleCustomSearch_2.data.instance}}"], "supervisor": "{{supervisor_0.data.instance}}", "model": "", "promptValues": "{\"company\":\"Flowise Inc\",\"portfolio_name\":\"Tesla Inc\"}", "maxIterations": ""}, "outputAnchors": [{"id": "worker_2-output-worker-Worker", "name": "worker", "label": "Worker", "description": "", "type": "Worker"}], "outputs": {}, "selected": false}, "width": 300, "height": 808, "selected": false, "positionAbsolute": {"x": 1482.836195011232, "y": 119.54481208270889}, "dragging": false}, {"id": "chatOpenAI_0", "position": {"x": -120.80560304817006, "y": 71.63806380387018}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_0-input-modelName-asyncOptions"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-4o", "temperature": "0", "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": "", "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 669, "selected": false, "positionAbsolute": {"x": -120.80560304817006, "y": 71.63806380387018}, "dragging": false}, {"id": "googleCustomSearch_0", "position": {"x": 268.39206549032804, "y": -209.224097209214}, "type": "customNode", "data": {"id": "googleCustomSearch_0", "label": "Google Custom Search", "version": 1, "name": "googleCustomSearch", "type": "GoogleCustomSearchAPI", "baseClasses": ["GoogleCustomSearchAPI", "Tool", "StructuredTool", "Runnable"], "category": "Tools", "description": "Wrapper around Google Custom Search API - a real-time API to access Google search results", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["googleCustomSearchApi"], "id": "googleCustomSearch_0-input-credential-credential"}], "inputAnchors": [], "inputs": {}, "outputAnchors": [{"id": "googleCustomSearch_0-output-googleCustomSearch-GoogleCustomSearchAPI|Tool|StructuredTool|Runnable", "name": "googleCustomSearch", "label": "GoogleCustomSearchAPI", "description": "Wrapper around Google Custom Search API - a real-time API to access Google search results", "type": "GoogleCustomSearchAPI | Tool | StructuredTool | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 275, "selected": false, "positionAbsolute": {"x": 268.39206549032804, "y": -209.224097209214}, "dragging": false}, {"id": "googleCustomSearch_1", "position": {"x": 708.2007597123056, "y": -214.21906914647434}, "type": "customNode", "data": {"id": "googleCustomSearch_1", "label": "Google Custom Search", "version": 1, "name": "googleCustomSearch", "type": "GoogleCustomSearchAPI", "baseClasses": ["GoogleCustomSearchAPI", "Tool", "StructuredTool", "Runnable"], "category": "Tools", "description": "Wrapper around Google Custom Search API - a real-time API to access Google search results", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["googleCustomSearchApi"], "id": "googleCustomSearch_1-input-credential-credential"}], "inputAnchors": [], "inputs": {}, "outputAnchors": [{"id": "googleCustomSearch_1-output-googleCustomSearch-GoogleCustomSearchAPI|Tool|StructuredTool|Runnable", "name": "googleCustomSearch", "label": "GoogleCustomSearchAPI", "description": "Wrapper around Google Custom Search API - a real-time API to access Google search results", "type": "GoogleCustomSearchAPI | Tool | StructuredTool | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 275, "selected": false, "positionAbsolute": {"x": 708.2007597123056, "y": -214.21906914647434}, "dragging": false}, {"id": "googleCustomSearch_2", "position": {"x": 1148.6913242910439, "y": -216.29397639610963}, "type": "customNode", "data": {"id": "googleCustomSearch_2", "label": "Google Custom Search", "version": 1, "name": "googleCustomSearch", "type": "GoogleCustomSearchAPI", "baseClasses": ["GoogleCustomSearchAPI", "Tool", "StructuredTool", "Runnable"], "category": "Tools", "description": "Wrapper around Google Custom Search API - a real-time API to access Google search results", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["googleCustomSearchApi"], "id": "googleCustomSearch_2-input-credential-credential"}], "inputAnchors": [], "inputs": {}, "outputAnchors": [{"id": "googleCustomSearch_2-output-googleCustomSearch-GoogleCustomSearchAPI|Tool|StructuredTool|Runnable", "name": "googleCustomSearch", "label": "GoogleCustomSearchAPI", "description": "Wrapper around Google Custom Search API - a real-time API to access Google search results", "type": "GoogleCustomSearchAPI | Tool | StructuredTool | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 275, "selected": false, "positionAbsolute": {"x": 1148.6913242910439, "y": -216.29397639610963}, "dragging": false}], "edges": [{"source": "googleCustomSearch_0", "sourceHandle": "googleCustomSearch_0-output-googleCustomSearch-GoogleCustomSearchAPI|Tool|StructuredTool|Runnable", "target": "worker_0", "targetHandle": "worker_0-input-tools-Tool", "type": "buttonedge", "id": "googleCustomSearch_0-googleCustomSearch_0-output-googleCustomSearch-GoogleCustomSearchAPI|Tool|StructuredTool|Runnable-worker_0-worker_0-input-tools-Tool"}, {"source": "googleCustomSearch_1", "sourceHandle": "googleCustomSearch_1-output-googleCustomSearch-GoogleCustomSearchAPI|Tool|StructuredTool|Runnable", "target": "worker_1", "targetHandle": "worker_1-input-tools-Tool", "type": "buttonedge", "id": "googleCustomSearch_1-googleCustomSearch_1-output-googleCustomSearch-GoogleCustomSearchAPI|Tool|StructuredTool|Runnable-worker_1-worker_1-input-tools-Tool"}, {"source": "googleCustomSearch_2", "sourceHandle": "googleCustomSearch_2-output-googleCustomSearch-GoogleCustomSearchAPI|Tool|StructuredTool|Runnable", "target": "worker_2", "targetHandle": "worker_2-input-tools-Tool", "type": "buttonedge", "id": "googleCustomSearch_2-googleCustomSearch_2-output-googleCustomSearch-GoogleCustomSearchAPI|Tool|StructuredTool|Runnable-worker_2-worker_2-input-tools-Tool"}, {"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "supervisor_0", "targetHandle": "supervisor_0-input-model-BaseChatModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-supervisor_0-supervisor_0-input-model-BaseChatModel"}, {"source": "supervisor_0", "sourceHandle": "supervisor_0-output-supervisor-Supervisor", "target": "worker_0", "targetHandle": "worker_0-input-supervisor-Supervisor", "type": "buttonedge", "id": "supervisor_0-supervisor_0-output-supervisor-Supervisor-worker_0-worker_0-input-supervisor-Supervisor"}, {"source": "supervisor_0", "sourceHandle": "supervisor_0-output-supervisor-Supervisor", "target": "worker_1", "targetHandle": "worker_1-input-supervisor-Supervisor", "type": "buttonedge", "id": "supervisor_0-supervisor_0-output-supervisor-Supervisor-worker_1-worker_1-input-supervisor-Supervisor"}, {"source": "supervisor_0", "sourceHandle": "supervisor_0-output-supervisor-Supervisor", "target": "worker_2", "targetHandle": "worker_2-input-supervisor-Supervisor", "type": "buttonedge", "id": "supervisor_0-supervisor_0-output-supervisor-Supervisor-worker_2-worker_2-input-supervisor-Supervisor"}]}